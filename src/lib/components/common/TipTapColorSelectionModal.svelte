<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import { FontColorOutline, CloseOutline, CheckOutline } from 'flowbite-svelte-icons';

    export let isOpen = false;
    export let selectedColor = 'black';

    const dispatch = createEventDispatcher();

    const colorOptions = [
        { name: "black", hex: "#000000" },
        { name: "gray", hex: "#6b7280" },
        { name: "red", hex: "#ef4444" },
        { name: "pink", hex: "#ec4899" },
        { name: "orange", hex: "#f97316" },
        { name: "yellow", hex: "#eab308" },
        { name: "purple", hex: "#a855f7" },
        { name: "violet", hex: "#8b5cf6" },
        { name: "blue", hex: "#3b82f6" },
        { name: "cyan", hex: "#06b6d4" },
        { name: "green", hex: "#22c55e" },
        { name: "lime", hex: "#84cc16" },
    ];

    function selectColor(colorName: string) {
        dispatch('colorSelected', { color: colorName });
        closeModal();
    }

    function closeModal() {
        dispatch('close');
    }

    function handleBackdropClick(event: MouseEvent) {
        if (event.target === event.currentTarget) {
            closeModal();
        }
    }

    function handleBackdropKeydown(event: KeyboardEvent) {
        if (event.key === 'Escape') {
            closeModal();
        }
    }

    function handleKeydown(event: KeyboardEvent) {
        if (event.key === 'Escape') {
            closeModal();
        }
    }


</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
    <!-- Modal backdrop -->
    <!-- svelte-ignore a11y-no-noninteractive-element-interactions -->
    <!-- svelte-ignore a11y-click-events-have-key-events -->
    <div
        class="modal-backdrop"
        on:click={handleBackdropClick}
        on:keydown={handleBackdropKeydown}
        role="dialog"
        aria-modal="true"
        aria-labelledby="color-modal-title"
        tabindex="-1"
    >
        <!-- Modal content -->
        <div class="modal-content">
            <!-- Modal header -->
            <div class="modal-header">
                <div class="modal-title-container">
                    <FontColorOutline class="w-5 h-5 text-gray-600" />
                    <h3 id="color-modal-title" class="modal-title">Select Text Color</h3>
                </div>
                <button 
                    class="close-button" 
                    on:click={closeModal}
                    aria-label="Close color selection"
                >
                    <CloseOutline class="w-4 h-4" />
                </button>
            </div>

            <!-- Color grid -->
            <div class="color-grid">
                {#each colorOptions as color (color.name)}
                    <button
                        class="color-option text-white"
                        class:selected={selectedColor === color.name}
                        style="background-color: {color.hex}"
                        on:click={() => selectColor(color.name)}
                        title="{color.name} ({color.hex})"
                        aria-label="Select {color.name} color"
                    >
                        {#if selectedColor === color.name}
                            <CheckOutline />
                        {/if}
                    </button>
                {/each}
            </div>

            <!-- Modal footer -->
            <!-- <div class="modal-footer">
                <button class="cancel-button" on:click={closeModal}>
                    Cancel
                </button>
            </div> -->
        </div>
    </div>
{/if}

<style>
    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        padding: 16px;
    }

    .modal-content {
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        max-width: 320px;
        width: 100%;
        max-height: 90vh;
        overflow: hidden;
        animation: modalSlideIn 0.2s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
        }
        to {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    .modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        border-bottom: 1px solid #e5e7eb;
    }

    .modal-title-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .modal-title {
        font-size: 16px;
        font-weight: 600;
        color: #111827;
        margin: 0;
    }

    .close-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: none;
        background: transparent;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
    }

    .close-button:hover {
        background-color: #f3f4f6;
        color: #374151;
    }

    .color-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
        padding: 20px;
        gap: 10px;
    }

    .color-option {
        position: relative;
        width: 32px;
        height: 32px;
        border-radius: 8px;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .color-option:hover {
        transform: scale(1.05);
        border-color: #d1d5db;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .color-option.selected {
        border-color: #374151;
        box-shadow: 0 0 0 2px #e5e7eb;
    }

    .selected-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.3);
    }

    .modal-footer {
        padding: 16px 20px;
        border-top: 1px solid #e5e7eb;
        display: flex;
        justify-content: flex-end;
    }

    .cancel-button {
        padding: 8px 16px;
        border-radius: 6px;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
    }

    .cancel-button:hover {
        background-color: #f9fafb;
        border-color: #9ca3af;
    }

    /* Responsive adjustments */
    @media (max-width: 480px) {
        .modal-content {
            max-width: 280px;
        }

        .color-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            padding: 16px;
        }

        .color-option {
            width: 44px;
            height: 44px;
        }
    }
</style>
